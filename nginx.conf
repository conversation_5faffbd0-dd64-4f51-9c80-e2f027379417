# Nginx configuration for Mossbets B2B Dashboard SPA
# This configuration handles client-side routing for Vue.js applications

server {
    listen 80;
    server_name b2b.bo.mb-gaming.life;  # Your actual domain
    root /var/www/html;     # Replace with your actual build directory path
    index index.html;

    # Enable gzip compression for better performance
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Handle API requests (if your API is on the same domain)
    location /api/ {
        # Proxy to your API server
        proxy_pass https://b2b.mb.mb-gaming.life/;  # Replace with your API URL
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Handle static assets (CSS, JS, images, etc.)
    location /app/assets/ {
        alias /var/www/html/assets/;  # Point to your build assets directory
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Handle /app/ subdirectory for SPA routing
    location /app/ {
        alias /var/www/html/;  # Point to your build directory
        try_files $uri $uri/ /app/index.html;
    }

    # Redirect root to /app/
    location = / {
        return 301 /app/;
    }

    # Handle all other routes - fallback for any missed routes
    location / {
        try_files $uri $uri/ /app/index.html;
    }

    # Optional: Handle specific file extensions
    location ~* \.(html)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # Error pages - redirect to app
    error_page 404 /app/index.html;
    error_page 500 502 503 504 /app/index.html;
}

# HTTPS configuration (recommended for production)
# Uncomment and configure the following block for HTTPS
#
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;  # Replace with your domain
#     root /var/www/html;
#     index index.html;
#
#     # SSL configuration
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#
#     # Include the same location blocks as above
#     # ... (copy all location blocks from the HTTP configuration)
# }
#
# # Redirect HTTP to HTTPS
# server {
#     listen 80;
#     server_name your-domain.com;
#     return 301 https://$server_name$request_uri;
# }
