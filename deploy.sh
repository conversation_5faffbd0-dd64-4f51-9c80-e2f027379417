#!/bin/bash

# Deployment script for Mossbets B2B Dashboard
# This script builds the application for production and prepares it for deployment to /app/ subdirectory

echo "🚀 Starting deployment process..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist

# Build for production
echo "🔨 Building for production..."
npm run build

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed! dist directory not found."
    exit 1
fi

echo "✅ Build completed successfully!"
echo ""
echo "📋 Deployment Instructions:"
echo "1. Upload the contents of the 'dist' folder to your server's /var/www/apps/mossbets_b2b_bo_ui_pro/ directory"
echo "2. Make sure your Nginx configuration is updated (use the nginx.conf file in this repo)"
echo "3. Restart Nginx: sudo systemctl restart nginx"
echo "4. Your app will be available at: https://b2b.bo.mb-gaming.life/app/"
echo ""
echo "🔧 Nginx Configuration:"
echo "- Copy nginx.conf to your server's Nginx sites-available directory"
echo "- Create a symlink in sites-enabled if needed"
echo "- Update the root path in nginx.conf to match your server setup"
echo ""
echo "🧪 Testing:"
echo "- Visit https://b2b.bo.mb-gaming.life/app/"
echo "- Navigate to different pages (e.g., /app/partners)"
echo "- Refresh the page to test SPA routing"
echo "- Test direct URL access"

# Optional: Show build size
if command -v du &> /dev/null; then
    echo ""
    echo "📊 Build size:"
    du -sh dist
fi

echo ""
echo "🎉 Deployment preparation complete!"
