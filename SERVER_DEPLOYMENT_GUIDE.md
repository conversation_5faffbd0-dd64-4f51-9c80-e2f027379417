# Server Deployment Guide - Mossbets B2B Dashboard

## Current Server Setup
- **Server Path**: `/var/www/apps/mossbets_b2b_bo_ui_pro`
- **URL**: `https://b2b.bo.mb-gaming.life/app`
- **Server**: Nginx

## Quick Fix for Asset Loading Issues

### Step 1: Build the Application
```bash
# On your local machine
npm run build
```

### Step 2: Upload Files to Correct Location
Upload ALL contents of the `dist` folder to:
```
/var/www/apps/mossbets_b2b_bo_ui_pro/
```

**Important**: Make sure the directory structure looks like this:
```
/var/www/apps/mossbets_b2b_bo_ui_pro/
├── index.html
├── assets/
│   ├── index-BDIZwinX.css
│   ├── index-C9QbvZYA.js
│   ├── vendor-DGybOs-S.js
│   └── [other asset files]
├── favicon.ico
└── [other files from dist]
```

### Step 3: Update Nginx Configuration
Use the updated `nginx.conf` file from this repository. Key sections:

```nginx
server {
    listen 80;
    server_name b2b.bo.mb-gaming.life;
    root /var/www/apps;
    index index.html;

    # Handle static assets
    location /app/assets/ {
        alias /var/www/apps/mossbets_b2b_bo_ui_pro/assets/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Handle /app/ subdirectory for SPA routing
    location /app/ {
        alias /var/www/apps/mossbets_b2b_bo_ui_pro/;
        try_files $uri $uri/ /app/index.html;
    }
    
    # Redirect root to /app/
    location = / {
        return 301 /app/;
    }
}
```

### Step 4: Set Correct Permissions
```bash
# On your server
sudo chown -R www-data:www-data /var/www/apps/mossbets_b2b_bo_ui_pro
sudo chmod -R 755 /var/www/apps/mossbets_b2b_bo_ui_pro
```

### Step 5: Test Nginx Configuration
```bash
# Test configuration
sudo nginx -t

# If test passes, restart Nginx
sudo systemctl restart nginx
```

### Step 6: Verify Deployment
1. **Test asset loading directly**:
   - Visit: `https://b2b.bo.mb-gaming.life/app/assets/index-BDIZwinX.css`
   - Should return the CSS file content, not 404

2. **Test the application**:
   - Visit: `https://b2b.bo.mb-gaming.life/app/`
   - Navigate to: `https://b2b.bo.mb-gaming.life/app/partners`
   - Refresh the page to test SPA routing

## Troubleshooting

### If assets still return 404:
1. **Check file exists**:
   ```bash
   ls -la /var/www/apps/mossbets_b2b_bo_ui_pro/assets/
   ```

2. **Check Nginx error logs**:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

3. **Check access logs**:
   ```bash
   sudo tail -f /var/log/nginx/access.log
   ```

### If you get permission errors:
```bash
sudo chown -R www-data:www-data /var/www/apps/mossbets_b2b_bo_ui_pro
sudo chmod -R 755 /var/www/apps/mossbets_b2b_bo_ui_pro
```

## Automated Deployment Script for Server

Create this script on your server (`/var/www/apps/deploy.sh`):

```bash
#!/bin/bash
# Server deployment script

DEPLOY_DIR="/var/www/apps/mossbets_b2b_bo_ui_pro"
BACKUP_DIR="/var/www/apps/backup_$(date +%Y%m%d_%H%M%S)"

echo "🚀 Starting deployment..."

# Create backup
if [ -d "$DEPLOY_DIR" ]; then
    echo "📦 Creating backup..."
    sudo cp -r "$DEPLOY_DIR" "$BACKUP_DIR"
fi

# Clear current deployment (keep backup)
echo "🧹 Clearing current deployment..."
sudo rm -rf "$DEPLOY_DIR"/*

# Extract new deployment (assuming you uploaded dist.tar.gz)
echo "📂 Extracting new files..."
sudo mkdir -p "$DEPLOY_DIR"
sudo tar -xzf /tmp/dist.tar.gz -C "$DEPLOY_DIR"

# Set permissions
echo "🔐 Setting permissions..."
sudo chown -R www-data:www-data "$DEPLOY_DIR"
sudo chmod -R 755 "$DEPLOY_DIR"

# Test nginx config
echo "🔧 Testing Nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx config is valid, restarting..."
    sudo systemctl restart nginx
    echo "🎉 Deployment complete!"
    echo "🌐 Visit: https://b2b.bo.mb-gaming.life/app/"
else
    echo "❌ Nginx config error! Check configuration."
    exit 1
fi
```

## Expected URLs After Deployment
- **Root**: `https://b2b.bo.mb-gaming.life/` → Redirects to `/app/`
- **App**: `https://b2b.bo.mb-gaming.life/app/`
- **Partners**: `https://b2b.bo.mb-gaming.life/app/partners`
- **Assets**: `https://b2b.bo.mb-gaming.life/app/assets/[filename]`
