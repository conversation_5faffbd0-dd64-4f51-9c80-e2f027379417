<template>
  <!-- Partner Dashboard -->
  <PartnerDashboard v-if="authStore.isPartner" />

  <!-- Admin Dashboard -->
  <div v-else class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome back{{ userName ? `, ${userName}` : '' }}!</h2>
          <p class="text-gray-600 dark:text-gray-300 mt-1">Here's what's happening with your financial platform today.</p>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Privacy Toggle -->
          <button
            @click="authStore.togglePrivateMode()"
            :title="getPrivacyTooltip(authStore.isPrivateMode)"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <span class="mr-2">{{ getPrivacyIcon(authStore.isPrivateMode) }}</span>
            {{ authStore.isPrivateMode ? 'Show Data' : 'Hide Data' }}
          </button>

          <!-- Date Range Picker -->
          <DateRangePicker
            v-model="dateRange"
            @change="onDateRangeChange"
            placeholder="Select date range"
            position="right"
          />

          <!-- Today's Date -->
          <div class="hidden md:block">
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Today's Date</div>
              <div class="text-blue-900 dark:text-blue-300 text-lg font-semibold">{{ currentDate }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600 dark:text-gray-300">Loading dashboard data...</span>
    </div>

    <!-- Dashboard Content -->
    <div v-else>
      <!-- Dashboard Cards -->
      <DashboardCards :cards="dashboardData.cards" />
    </div>

    <!-- Charts and Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Transactions -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Transactions</h3>
          <router-link :to="{ name: 'transactions' }" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View all
          </router-link>
        </div>
        <div class="space-y-4">
          <div class="text-center text-gray-500 dark:text-gray-400 py-8">
            <p>Recent transactions will be displayed here</p>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
          <!-- <router-link :to="{ name: 'organisations' }" class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200">
            <BuildingOfficeIcon class="w-8 h-8 text-blue-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">Manage Organizations</p>
          </router-link>
          <router-link :to="{ name: 'requests' }" class="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200">
            <CurrencyDollarIcon class="w-8 h-8 text-green-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">Loan Requests</p>
          </router-link>
          <router-link :to="{ name: 'customers' }" class="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors duration-200">
            <MagnifyingGlassIcon class="w-8 h-8 text-purple-600 mb-2" />
            <p class="text-sm font-medium text-gray-900">Search Customers</p>
          </router-link> -->
          <router-link :to="{ name: 'system-roles' }" class="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors duration-200">
            <UsersIcon class="w-8 h-8 text-orange-600 mb-2" />
            <p class="text-sm font-medium text-gray-900 dark:text-white">System Roles</p>
          </router-link>
        </div>
      </div>
    </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        <!-- Partner Balance Chart -->
        <div v-if="canViewChart('PARTNER_BALANCE')" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ dashboardData?.charts?.partner_balance?.title || 'Partner Balance' }}</h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">{{ dashboardData?.filters?.period || '7days' }}</div>
          </div>
          <div class="h-80">
            <DoughnutChart
              v-if="partnerBalanceChartData.labels.length > 0"
              :data="partnerBalanceChartData"
              :options="doughnutChartOptions"
              :height="320"
            />
            <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              No data available
            </div>
          </div>
        </div>

        <!-- Partner Performance Chart -->
        <div v-if="canViewChart('PARTNER_PERFORMANCE')" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ dashboardData?.charts?.partner_performance?.title || 'Partner Performance' }}</h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">{{ dashboardData?.filters?.period || '7days' }}</div>
          </div>
          <div class="h-80">
            <BarChart
              v-if="partnerPerformanceChartData.labels.length > 0"
              :data="partnerPerformanceChartData"
              :options="barChartOptions"
              :height="320"
            />
            <div v-else class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              No data available
            </div>
          </div>
        </div>
      </div>
    </div>

</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { dashboardApi, type EnhancedDashboardData } from '@/services/dashboardApi'
import { dashboardCache } from '@/services/dashboardCache'
import { getPrivacyIcon, getPrivacyTooltip } from '@/utils/privacy'
import { canViewChart } from '@/utils/permissions'
import { usePartnerSelection } from '@/composables/usePartnerSelection'
import PartnerDashboard from '@/views/PartnerDashboard.vue'
import DashboardCards from '@/components/Dashboard/DashboardCards.vue'
import BarChart from '@/components/Charts/BarChart.vue'
import DoughnutChart from '@/components/Charts/DoughnutChart.vue'
import DateRangePicker from '@/components/DateRangePicker.vue'
import { UsersIcon } from '@heroicons/vue/24/outline'

// Types for date range
interface DateRange {
  startDate: string | null
  endDate: string | null
}

// Auth store
const authStore = useAuthStore()
const { onPartnerSelectionChange } = usePartnerSelection()

// Reactive data
const loading = ref(false)

// Enhanced dashboard data
const dashboardData = ref<EnhancedDashboardData>({
  cards: {
    users: { total: 0, active: 0, inactive: 0, suspended: 0, new_this_month: 0 },
    partners: { total: 0, active: 0, total_balance: '0.00', average_balance: '0.00', highest_balance: '0.00', total_bonus: '0.00', average_bonus: '0.00', highest_bonus: '0.00', new_this_month: 0 },
    betting: { total_bets: 0, total_stakes: '0.00', total_winnings: '0.00', net_revenue: '0.00', average_stake: '0.00', betting_partners: 0 },
    system: { total_roles: 0, total_permissions: 0, active_channels: 0, period: '7days' }
  },
  charts: {
    partner_balance: { title: 'Partner Balance', type: 'doughnut', data: [] },
    partner_performance: { title: 'Partner Performance', type: 'bar', data: [] }
  },
  filters: { partner_ids: '', period: '7days', chart_type: 'all', start_date: '', end_date: '', date_filter_applied: false }
})

// Date range picker
const dateRange = ref<DateRange>({
  startDate: null,
  endDate: null
})

// User name from auth store
const userName = computed(() => {
  return authStore.user?.display_name || authStore.user?.username || null
})

// Current date
const currentDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Chart data computed properties
const partnerBalanceChartData = computed(() => {
  const charts = dashboardData.value?.charts
  const balanceChart = charts?.partner_balance
  const data = balanceChart?.data

  if (!data || !Array.isArray(data) || data.length === 0) {
    return { labels: [], datasets: [] }
  }

  return {
    labels: data.map(item => item?.partner_name || 'Unknown'),
    datasets: [{
      data: data.map(item => parseFloat(item?.balance || item?.net_revenue || '0')),
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(236, 72, 153, 0.8)',
        'rgba(34, 197, 94, 0.8)',
        'rgba(251, 146, 60, 0.8)',
        'rgba(168, 85, 247, 0.8)',
        'rgba(14, 165, 233, 0.8)'
      ],
      borderColor: [
        'rgb(59, 130, 246)',
        'rgb(16, 185, 129)',
        'rgb(245, 158, 11)',
        'rgb(239, 68, 68)',
        'rgb(139, 92, 246)',
        'rgb(236, 72, 153)',
        'rgb(34, 197, 94)',
        'rgb(251, 146, 60)',
        'rgb(168, 85, 247)',
        'rgb(14, 165, 233)'
      ],
      borderWidth: 2
    }]
  }
})

const partnerPerformanceChartData = computed(() => {
  const charts = dashboardData.value?.charts
  const performanceChart = charts?.partner_performance
  const data = performanceChart?.data

  if (!data || !Array.isArray(data) || data.length === 0) {
    return { labels: [], datasets: [] }
  }

  return {
    labels: data.map(item => item?.partner_name || 'Unknown'),
    datasets: [{
      label: 'Net Revenue',
      data: data.map(item => parseFloat(item?.net_revenue || '0')),
      backgroundColor: 'rgba(59, 130, 246, 0.8)',
      borderColor: 'rgb(59, 130, 246)',
      borderWidth: 1
    }]
  }
})

// Chart options
const doughnutChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const
    }
  }
}

const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: function(value: any) {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
          }).format(value)
        }
      }
    }
  }
}

// Methods
const loadEnhancedDashboard = async (forceRefresh = false) => {
  loading.value = true
  try {
    const params = {
      partner_ids: authStore.selectedPartnerId || '',
      period: dashboardData.value.filters.period,
      start_date: dateRange.value.startDate || '',
      end_date: dateRange.value.endDate || '',
      chart_type: 'all'
    }

    // Try to get cached data first (unless force refresh is requested)
    if (!forceRefresh) {
      const cachedData = dashboardCache.get(params)
      if (cachedData) {
        dashboardData.value = cachedData
        loading.value = false
        return
      }
    }

    const response = await dashboardApi.getEnhancedDashboard(params)

    if (response.status === 200 && response.message) {
      // Ensure the response has the expected structure
      const responseData = response.message
      dashboardData.value = {
        cards: responseData.cards || {
          users: { total: 0, active: 0, inactive: 0, suspended: 0, new_this_month: 0 },
          partners: { total: 0, active: 0, total_balance: '0.00', average_balance: '0.00', highest_balance: '0.00', total_bonus: '0.00', average_bonus: '0.00', highest_bonus: '0.00', new_this_month: 0 },
          betting: { total_bets: 0, total_stakes: '0.00', total_winnings: '0.00', net_revenue: '0.00', average_stake: '0.00', betting_partners: 0 },
          system: { total_roles: 0, total_permissions: 0, active_channels: 0, period: '7days' }
        },
        charts: responseData.charts || {
          partner_balance: { title: 'Partner Balance', type: 'doughnut', data: [] },
          partner_performance: { title: 'Partner Performance', type: 'bar', data: [] }
        },
        filters: responseData.filters || { partner_ids: '', period: '7days', chart_type: 'all', start_date: '', end_date: '', date_filter_applied: false }
      }
      // Cache the successful response
      dashboardCache.set(params, dashboardData.value)
    }
  } catch (error) {
    console.error('Failed to load enhanced dashboard:', error)
  } finally {
    loading.value = false
  }
}



const onDateRangeChange = (range: DateRange) => {
  dateRange.value = range
  // Force refresh when date range changes
  loadEnhancedDashboard(true)
}

// Watch for partner selection changes
watch(() => authStore.selectedPartnerId, () => {
  loadEnhancedDashboard()
}, { immediate: false })

// Lifecycle
// Use partner selection composable for automatic refresh
onPartnerSelectionChange(() => loadEnhancedDashboard(true))

onMounted(() => {
  loadEnhancedDashboard()
})



</script>
