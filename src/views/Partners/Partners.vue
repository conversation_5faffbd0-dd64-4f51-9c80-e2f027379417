<template>
  <div class="space-y-6">
    <!-- <PERSON>er -->
    <!-- <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Partners</h1>
          <p class="mt-1 text-sm text-gray-600">
            Manage business partners and their integrations
          </p>
        </div>
        <div class="flex space-x-3">
          <button @click="refreshData" :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ loading ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button @click="exportData"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </button>
        </div>
      </div>
    </div> -->

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
          <select id="status-filter" v-model="filters.status" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Statuses</option>
            <option value="1">Active</option>
            <option value="0">Inactive</option>
            <option value="3">Suspended</option>
          </select>
        </div>
        <div>
          <label for="region-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Country</label>
          <select id="region-filter" v-model="filters.region" @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
            <option value="">All Countries</option>
            <option v-for="country in countries" :key="country.code" :value="country.code">
              {{ country.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
          <DateRangePicker
            v-model="dateRange"
            placeholder="Select date range"
            @change="handleDateRangeChange"
          />
        </div>
        <div class="flex items-end">
        </div>
      </div>
    </div>

    <!-- Partners Table -->
    <DataTable :data="partners" :headers="partnerHeaders"  :loading="loading" :current-page="currentPage" :total-records="totalRecords"
      :page-size="pageSize" title="Partners" row-key="id" :has-actions="true" @page-change="handlePageChange"
      @search="handleSearch" @sort="handleSort" @row-click="handleRowClick" @limit-change="handleLimitChange">
      <!-- Header Actions Slot -->
      <template #header-actions>
        <button @click="exportData"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export
        </button>

        <div class="flex space-x-2">
          <router-link :to="{ name: 'partner-add' }"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Partner
          </router-link>
        </div>

          <button
            @click="clearFilters"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-orange-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Clear Filters
          </button>

        <button @click="refreshData" :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-blue-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
          <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {{ loading ? 'Refreshing...' : 'Refresh' }}
        </button>
        
      </template>

      <!-- Custom Status Cell -->
      <template #cell-status="{ value }">
        <span :class="{
          'bg-green-100 text-green-800': value === '1' || value === 1,
          'bg-red-100 text-red-800': value === '0' || value === 0,
          'bg-orange-100 text-orange-800': value === '3' || value === 3
        }" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
          {{ value === '1' ? 'Active' : value === '0' ? 'Inactive' : value === '3' ? 'Suspended' : '-' }}
        </span>
      </template>

      <!-- Custom Email Address Cell -->
      <template #cell-email_address="{ value,item }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {{ value ? value : '-' }}
        </span>
        <br>
        
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {{ item.msisdn ?? '-' }}
        </span>
      </template>

      <!-- Actions -->
      <template #actions="{ item, closeDropdown }">
        <button @click="viewPartner(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-100 transition-colors duration-200">
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          View Partner
        </button>
        <!-- <button
          v-if="canEditPartner"
          @click="editPartner(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit Partner
        </button>
        <button
          @click="manageServices(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          View Partner Services
        </button>
        <button
          @click="viewBets(item); closeDropdown()"
          class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="inline w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
          </svg>
          View Partner Bets
        </button> -->
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import DateRangePicker from '@/components/DateRangePicker.vue'
import { partnerApi } from '@/services/partnerApi'
import { useAuthStore } from '@/stores/auth'
import { navigateWithCache } from '@/utils/navigationCache'
import { countries } from '@/utils/countries.ts'
// Router and stores
const router = useRouter()
const authStore = useAuthStore()

// Computed permissions
const canEditPartner = computed(() => {
  return authStore.hasAnyRole([1, 2]) || authStore.isSuperUser
})

// Reactive data
const loading = ref(false)
const partners = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)

// Filters
const filters = reactive({
  status: '',
  partner_type: '',
  region: ''
})

// Date range for filtering
const dateRange = ref({
  startDate: null as string | null,
  endDate: null as string | null
})

const partnerHeaders = computed(() => ({
  name: 'Partner Name',
  email_address: 'Email/Phone',
  address: 'Address',
  country: 'Country',
  status: 'Status',
  created: 'Created',
  updated: 'Updated'
}))

// Methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await partnerApi.getPartners({
      page: currentPage.value,
      limit: pageSize.value,
      name: searchQuery.value, // Use name parameter for partner name search
      status: filters.status,
      start: dateRange.value.startDate || '',
      end: dateRange.value.endDate || '',
      showErr:false
    })

    // console.log("Partners page Api Response: "+ JSON.stringify(response.message.data))

    if (response.status === 200) {
      partners.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    } else {
      console.error('Failed to load partners:', response)

      partners.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error loading partners:', JSON.stringify(error))
    partners.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewPartner(item)
}

const handleLimitChange = (limit: number) => {
  pageSize.value = limit
  currentPage.value = 1
  loadData()
}

const handleDateRangeChange = (range: { startDate: string | null, endDate: string | null }) => {
  dateRange.value = range
  applyFilters()
}

const clearFilters = () => {
  filters.status = ''
  filters.partner_type = ''
  filters.region = ''
  dateRange.value = { startDate: null, endDate: null }
  searchQuery.value = ''
  applyFilters()
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewPartner = (partner: any) => {
  // Navigate with cached data
  console.log('Navigating to view partner with ID:', partner.id)
  navigateWithCache(
    router,
    partner,
    'partner',
    'partner-details',
    { id: partner.id.toString() }
  )
}

const editPartner = (partner: any) => {
  // Navigate with cached data in edit mode
  console.log('Navigating to edit partner with ID:', partner.id)
  navigateWithCache(
    router,
    partner,
    'partner',
    'partner-details',
    { id: partner.id.toString() },
    { mode: 'edit' }
  )
}

const manageServices = (partner: any) => {
  router.push({ name: 'partner-services', query: { partner_id: partner.id } })
}

const viewBets = (partner: any) => {
  router.push({ name: 'partner-bets', query: { partner_id: partner.id } })
}

const exportData = () => {
  // Export partners data to CSV or Excel format
  // Implementation pending API endpoint
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
