<template>
  <div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome back{{ userName ? `, ${userName}` : '' }}!</h2>
          <p class="text-gray-600 dark:text-gray-300 mt-1">Here's your partner performance overview.</p>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Privacy Toggle -->
          <button
            @click="authStore.togglePrivateMode()"
            :title="getPrivacyTooltip(authStore.isPrivateMode)"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <span class="mr-2">{{ getPrivacyIcon(authStore.isPrivateMode) }}</span>
            {{ authStore.isPrivateMode ? 'Show Data' : 'Hide Data' }}
          </button>

          <!-- Date Range Picker -->
          <DateRangePicker
            v-model="dateRange"
            @change="onDateRangeChange"
            placeholder="Select date range"
            position="right"
          />

          <!-- Partner Status -->
          <div class="hidden md:block">
            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div class="text-green-600 dark:text-green-400 text-sm font-medium">Partner Status</div>
              <div class="text-green-900 dark:text-green-300 text-lg font-semibold">Active</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Partner Stats Grid -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div v-for="i in 4" :key="i" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-gray-200 dark:bg-gray-700 w-12 h-12"></div>
          <div class="ml-4 flex-1">
            <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Net Revenue (Primary Focus) -->
      <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-sm p-6 text-white">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-white/20">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-white/80">Net Revenue</p>
            <p class="text-2xl font-semibold text-white">{{ formatCurrencyPrivacy(dashboardData.cards.betting.net_revenue, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-white/80 text-sm">Average Stake: {{ formatCurrencyPrivacy(dashboardData.cards.betting.average_stake, authStore.isPrivateMode) }}</span>
        </div>
      </div>

      <!-- Total Bets -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Bets</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatNumberPrivacy(dashboardData.cards.betting.total_bets, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-blue-600 dark:text-blue-400 text-sm font-medium">{{ formatNumberPrivacy(dashboardData.cards.betting.betting_partners, authStore.isPrivateMode) }} betting partners</span>
        </div>
      </div>

      <!-- Total Stakes -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Stakes</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatCurrencyPrivacy(dashboardData.cards.betting.total_stakes, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4">
          <span class="text-purple-600 dark:text-purple-400 text-sm font-medium">Winnings: {{ formatCurrencyPrivacy(dashboardData.cards.betting.total_winnings, authStore.isPrivateMode) }}</span>
        </div>
      </div>

      <!-- Partner Balance -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-lg bg-orange-100 dark:bg-orange-900/20">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Partner Balance</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ formatCurrencyPrivacy(dashboardData.cards.partners.total_balance, authStore.isPrivateMode) }}</p>
          </div>
        </div>
        <div class="mt-4 space-y-1">
          <div class="text-xs text-gray-500 dark:text-gray-400">
            <span>Bonus: {{ formatCurrencyPrivacy(dashboardData.cards.partners.total_bonus, authStore.isPrivateMode) }}</span>
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            <span>Period: {{ dashboardData.cards.system.period }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <router-link
          :to="{ name: 'partners-bets' }"
          class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        >
          <div class="p-2 bg-blue-100 dark:bg-blue-900/40 rounded-lg">
            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">View Bets</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Manage betting activity</p>
          </div>
        </router-link>

        <router-link
          :to="{ name: 'partners-bet-slips' }"
          class="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
        >
          <div class="p-2 bg-green-100 dark:bg-green-900/40 rounded-lg">
            <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">Bet Slips</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Review bet slips</p>
          </div>
        </router-link>

        <router-link
          :to="{ name: 'partner-services' }"
          class="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
        >
          <div class="p-2 bg-purple-100 dark:bg-purple-900/40 rounded-lg">
            <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">Services</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Manage services</p>
          </div>
        </router-link>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Bets -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Bets</h3>
        <div class="space-y-3">
          <div v-for="bet in recentBets" :key="bet.id" class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ bet.description }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ bet.time }}</p>
            </div>
            <div class="text-right">
              <p class="text-sm font-semibold" :class="bet.status === 'won' ? 'text-green-600 dark:text-green-400' : bet.status === 'lost' ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'">
                KES {{ formatCurrency(bet.amount) }}
              </p>
              <p class="text-xs capitalize" :class="bet.status === 'won' ? 'text-green-600 dark:text-green-400' : bet.status === 'lost' ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400'">
                {{ bet.status }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Chart Placeholder -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Overview</h3>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="text-center">
            <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            <p class="text-gray-500 dark:text-gray-400">Chart will be added here</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { dashboardApi, type EnhancedDashboardData } from '@/services/dashboardApi'
import { dashboardCache } from '@/services/dashboardCache'
import { getPrivacyIcon, getPrivacyTooltip, formatCurrencyPrivacy, formatNumberPrivacy } from '@/utils/privacy'
import { usePartnerSelection } from '@/composables/usePartnerSelection'
import DateRangePicker from '@/components/DateRangePicker.vue'

// Types for date range
interface DateRange {
  startDate: string | null
  endDate: string | null
}

// Stores
const authStore = useAuthStore()
const { onPartnerSelectionChange } = usePartnerSelection()

// Reactive data
const loading = ref(true)

// Enhanced dashboard data for partners
const dashboardData = ref<EnhancedDashboardData>({
  cards: {
    users: { total: 0, active: 0, inactive: 0, suspended: 0, new_this_month: 0 },
    partners: { total: 0, active: 0, total_balance: '0.00', average_balance: '0.00', highest_balance: '0.00', total_bonus: '0.00', average_bonus: '0.00', highest_bonus: '0.00', new_this_month: 0 },
    betting: { total_bets: 0, total_stakes: '0.00', total_winnings: '0.00', net_revenue: '0.00', average_stake: '0.00', betting_partners: 0 },
    system: { total_roles: 0, total_permissions: 0, active_channels: 0, period: '7days' }
  },
  charts: {
    partner_balance: { title: 'Partner Balance', type: 'doughnut', data: [] },
    partner_performance: { title: 'Partner Performance', type: 'bar', data: [] }
  },
  filters: { partner_ids: '', period: '7days', chart_type: 'all', start_date: '', end_date: '', date_filter_applied: false }
})

// Date range picker
const dateRange = ref<DateRange>({
  startDate: null,
  endDate: null
})

// User name from auth store
const userName = computed(() => {
  return authStore.user?.display_name || authStore.user?.username || null
})

// Methods
const formatCurrency = (num: number): string => {
  return new Intl.NumberFormat('en-KE', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num)
}

const loadEnhancedPartnerDashboard = async (forceRefresh = false) => {
  loading.value = true
  try {
    const params = {
      partner_ids: authStore.selectedPartnerId || '',
      period: dashboardData.value.filters.period,
      start_date: dateRange.value.startDate || '',
      end_date: dateRange.value.endDate || '',
      chart_type: 'all'
    }

    // Try to get cached data first (unless force refresh is requested)
    if (!forceRefresh) {
      const cachedData = dashboardCache.get(params)
      if (cachedData) {
        dashboardData.value = cachedData
        loading.value = false
        return
      }
    }

    const response = await dashboardApi.getEnhancedDashboard(params)

    if (response.status === 200 && response.message) {
      // Ensure the response has the expected structure
      const responseData = response.message
      dashboardData.value = {
        cards: responseData.cards || {
          users: { total: 0, active: 0, inactive: 0, suspended: 0, new_this_month: 0 },
          partners: { total: 0, active: 0, total_balance: '0.00', average_balance: '0.00', highest_balance: '0.00', total_bonus: '0.00', average_bonus: '0.00', highest_bonus: '0.00', new_this_month: 0 },
          betting: { total_bets: 0, total_stakes: '0.00', total_winnings: '0.00', net_revenue: '0.00', average_stake: '0.00', betting_partners: 0 },
          system: { total_roles: 0, total_permissions: 0, active_channels: 0, period: '7days' }
        },
        charts: responseData.charts || {
          partner_balance: { title: 'Partner Balance', type: 'doughnut', data: [] },
          partner_performance: { title: 'Partner Performance', type: 'bar', data: [] }
        },
        filters: responseData.filters || { partner_ids: '', period: '7days', chart_type: 'all', start_date: '', end_date: '', date_filter_applied: false }
      }
      // Cache the successful response
      dashboardCache.set(params, dashboardData.value)
    }
  } catch (error) {
    console.error('Failed to load enhanced partner dashboard:', error)
  } finally {
    loading.value = false
  }
}





const onDateRangeChange = (range: DateRange) => {
  dateRange.value = range
  // Force refresh when date range changes
  loadEnhancedPartnerDashboard(true)
}

// Watch for partner selection changes
watch(() => authStore.selectedPartnerId, () => {
  loadEnhancedPartnerDashboard()
}, { immediate: false })

// Use partner selection composable for automatic refresh
onPartnerSelectionChange(() => loadEnhancedPartnerDashboard(true))

// Lifecycle
onMounted(() => {
  loadEnhancedPartnerDashboard()
})

// Sample recent bets
const recentBets = ref([
  { id: 1, description: 'Football Match - Arsenal vs Chelsea', amount: 5000, status: 'won', time: '2 minutes ago' },
  { id: 2, description: 'Basketball - Lakers vs Warriors', amount: 2500, status: 'pending', time: '15 minutes ago' },
  { id: 3, description: 'Tennis - Djokovic vs Nadal', amount: 1200, status: 'lost', time: '1 hour ago' },
  { id: 4, description: 'Football - Manchester United vs Liverpool', amount: 3500, status: 'won', time: '2 hours ago' },
  { id: 5, description: 'Cricket - India vs Australia', amount: 800, status: 'pending', time: '3 hours ago' }
])
</script>
