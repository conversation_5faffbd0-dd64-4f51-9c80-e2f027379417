<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Add System User</h1>
          <p class="text-gray-600 dark:text-gray-300 mt-1">Create a new system user account with role and permissions</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Users
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
        <span class="ml-3 text-gray-600 dark:text-gray-300">Loading...</span>
      </div>
    </div>

    <!-- Form -->
    <div v-else class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <form @submit.prevent="createUser" class="space-y-6">
        <!-- Client Selection (for non-super users) -->
        <div v-if="!authStore.isSuperUser" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Organisation <span v-if="clientName" class="text-gray-500 dark:text-gray-400">({{ clientName }})</span>
          </label>
          <div class="relative">
            <input
              v-model="searchClient"
              @click="toggleSearchDropdown"
              :placeholder="searchDropdownPlaceholder"
              class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              readonly
            />
            <div
              v-if="searchDropdown"
              class="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg max-h-60 rounded-md py-1 text-base overflow-auto focus:outline-none sm:text-sm"
            >
              <div
                v-for="item in organisations"
                :key="item.value"
                @click="setClientId(item)"
                class="cursor-pointer select-none relative py-2 pl-3 pr-9 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                {{ item.text }}
              </div>
            </div>
          </div>
        </div>

        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First Name *</label>
              <input
                v-model="userForm.first_name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter first name"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Middle Name</label>
              <input
                v-model="userForm.middle_name"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter middle name"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Name *</label>
              <input
                v-model="userForm.last_name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter last name"
              />
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address *</label>
              <input
                v-model="userForm.email_address"
                type="email"
                required
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter email address"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number (MSISDN) *</label>
              <div class="flex">
                <select
                  v-model="userForm.dial_code"
                  class="block px-3 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="254">+254</option>
                </select>
                <input
                  v-model="userForm.msisdn"
                  type="tel"
                  required
                  class="block w-full px-3 py-2 border-t border-r border-b border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-r-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="712345678"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Identity Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Identity Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
           
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ID Type *</label>
              <select
                v-model="userForm.user_type"
                required
                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Select ID Type</option>
                <option v-for="type in user_types" :key="type.value" :value="type.value">
                  {{ type.text }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- Role Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Role & Permissions</h3>
          <RoleTemplateSelector
            v-model="userForm.role_id"
            @role-selected="onRoleSelected"
            @permissions-changed="onPermissionsChanged"
          />
        </div>

        <!-- Partner Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Partner Assignment</h3>
          <PartnerSearchSelector
            v-model="userForm.partners"
            :user-type="userForm.user_type"
            :required="true"
            @partners-changed="onPartnersChanged"
            @validation-error="onPartnerValidationError"
          />
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <div v-if="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Create User
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { systemApi, type Role, type Permission } from '@/services/systemApi'
import { useAuthStore } from '@/stores/auth'
import { useSweetAlert } from '@/composables/useSweetAlert'
import { useGlobalLoading } from '@/stores/loading'
import RoleTemplateSelector from '@/components/Forms/RoleTemplateSelector.vue'
import PartnerSearchSelector from '@/components/Forms/PartnerSearchSelector.vue'

// Router and stores
const router = useRouter()
const authStore = useAuthStore()
const { showSuccess, showError, confirmAction } = useSweetAlert()
const { loadingWrapper } = useGlobalLoading()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const roles = ref<Role[]>([])
const partnerValidationError = ref<string | null>(null)
const selectedRoleTemplate = ref<Role | null>(null)

// Client/Organization search state
const searchClient = ref('')
const searchDropdown = ref(false)
const searchDropdownPlaceholder = ref('Select Organisation')
const clientName = ref('')
const organisations = ref<any[]>([])

// Form state
const userForm = ref({
  // Basic contact info
  dial_code: '254',
  msisdn: '',
  email_address: '',
  // Personal info
  first_name: '',
  middle_name: '',
  last_name: '',
  // Account info
  user_name: '',
  display_name: '',
  password: 'admin1234',
  // Role and permissions
  role_id: '',
  user_type: 'Partner' as 'Admin' | 'Partner',
  status: 1,
  permissions_acl: '',
  permissions: [] as number[],
  partners: [] as number[],
  // Database fields for API
  type: 'Partner' as string,
  partner_ids: [] as number[], // Will be mapped from partners
  created_by: '', // Will be set from auth store
  timestamp: Date.now()
})

const user_types = [
  { text: 'Partner', value: 'Partner' },
  { text: 'Admin', value: 'Admin' },
]

// Computed properties
const selectedRole = computed(() => {
  return selectedRoleTemplate.value
})

const isFormValid = computed(() => {
  const basicValid = userForm.value.first_name &&
         userForm.value.last_name &&
         userForm.value.email_address &&
         userForm.value.msisdn &&
         userForm.value.role_id &&
         userForm.value.user_type

  const partnerValid = !partnerValidationError.value &&
                      (userForm.value.user_type === 'Admin' || userForm.value.partners.length > 0)

  return basicValid && partnerValid
})

// Methods
const setClientId = (item: any) => {
  clientName.value = item.text
  searchDropdown.value = false
  searchClient.value = ''
  searchDropdownPlaceholder.value = clientName.value
}

const toggleSearchDropdown = () => {
  searchDropdown.value = !searchDropdown.value
}

const onUsernameChange = () => {
  // Manual username change - don't auto-generate anymore
}

const onDisplayNameChange = () => {
  // Manual display name change - don't auto-generate anymore
}

const onRoleSelected = (role: Role | null, permissions: number[], userType: 'Admin' | 'Partner') => {
  selectedRoleTemplate.value = role
  userForm.value.permissions = permissions
  userForm.value.user_type = userType
  userForm.value.type = userType
  // Use colon separator as backend expects
  userForm.value.permissions_acl = permissions.join(':')

  // Auto-generate display name if not manually set
  if (userForm.value.first_name && userForm.value.last_name) {
    if (!userForm.value.display_name) {
      const nameParts = [
        userForm.value.first_name,
        userForm.value.middle_name,
        userForm.value.last_name
      ].filter(name => name && name.trim())
      userForm.value.display_name = nameParts.join(' ')
    }
  }

  // Set user_name to email address as per requirements
  if (userForm.value.email_address) {
    userForm.value.user_name = userForm.value.email_address
  }
}

const onPermissionsChanged = (permissions: number[]) => {
  userForm.value.permissions = permissions
  // Use colon separator as backend expects
  userForm.value.permissions_acl = permissions.join(':')
}

const onPartnersChanged = (partners: any[]) => {
  // Update partner_ids for API compatibility
  userForm.value.partner_ids = partners
}

const onPartnerValidationError = (error: string | null) => {
  partnerValidationError.value = error
}

const fetchOrganisations = async () => {
  // Organizations functionality removed as per requirements
}

const fetchRoles = async () => {
  try {
    const response = await systemApi.getRoles({ limit: 100 })
    if (response.status === 200) {
      roles.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching roles:', error)
    roles.value = []
  }
}

const createUser = async () => {
  if (!isFormValid.value) {
    showError('Validation Error', 'Please fill in all required fields')
    return
  }

  const confirmed = await confirmAction(
    'Create User',
    'Are you sure you want to create this user?',
    'Yes, create user'
  )

  if (!confirmed) return

  try {
    // Prepare display_name as 'all names together'
    const nameParts = [
      userForm.value.first_name,
      userForm.value.middle_name,
      userForm.value.last_name
    ].filter(name => name && name.trim())

    const finalDisplayName = userForm.value.display_name || nameParts.join(' ')

    // user_name should be email as per requirements
    const finalUserName = userForm.value.email_address

    // Prepare payload according to the new API structure
    const payload = {
      // New API structure fields
      timestamp: Date.now().toString(),
      user_name: finalUserName, // email address
      display_name: finalDisplayName, // all names together
      msisdn: userForm.value.msisdn,
      role_id: parseInt(userForm.value.role_id),
      type: userForm.value.user_type, // Admin/Partner
      permissions_acl: userForm.value.permissions_acl, // colon-separated format like '3:6:8:90:77'
      status: userForm.value.status || 1,
      partner_ids: userForm.value.partner_ids.join(','), // comma-separated format like "3,4,6"

      // Legacy fields for API compatibility
      username: finalUserName,
      email_address: userForm.value.email_address,
      permissions: userForm.value.permissions
    }

    console.log('Creating user with payload:', payload)

    const response = await loadingWrapper(
      () => systemApi.createUser(payload),
      'Creating user...',
      'create-user'
    )

    if (response.status === 200) {
      showSuccess('Success!', 'User created successfully!')
      router.push({ name: 'system-users' })
    } else {
      showError('Error', response.message || 'Failed to create user')
    }
  } catch (error) {
    console.error('Error creating user:', error)
    showError('Error', 'An unexpected error occurred while creating the user')
  }
}

const goBack = () => {
  router.push({ name: 'system-users' })
}

// Watch for email changes to update user_name
watch(() => userForm.value.email_address, (newEmail) => {
  if (newEmail) {
    userForm.value.user_name = newEmail
  }
})

// Initialize data on mount
onMounted(async () => {
  if (!authStore.isSuperUser && authStore.user) {
    // Set account number from auth store if available
    // userForm.value.account_number = authStore.user.id?.toString() || ''
  }

  await fetchOrganisations()

  initialLoading.value = false
})
</script>
