<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col overflow-hidden">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Select Partner
          </h3>
          <button
            @click="closeModal"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- Tabs -->
        <div class="flex border-b border-gray-200 dark:border-gray-700">
          <button
            @click="activeTab = 'search'"
            :class="[
              'flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'search'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            ]"
          >
            Search Partners
          </button>
          <button
            @click="activeTab = 'selected'"
            :class="[
              'flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'selected'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            ]"
          >
            Selected ({{ selectedPartners.length }})
          </button>
        </div>

        <!-- Search Tab Content -->
        <div v-if="activeTab === 'search'" class="flex-1 overflow-hidden flex flex-col">
          <div class="flex-1 p-4 overflow-y-auto">
          <!-- Search Bar (Admin Only) -->
          <div v-if="authStore.isSuperUser" class="mb-4">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
              <input
                v-model="searchQuery"
                @input="debouncedSearch"
                type="text"
                placeholder="Search partners..."
                class="block w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white"
              />
            </div>
          </div>
          <!-- All Partners Option (for both admin and partner users) -->
          <div class="mb-4">
            <label
              class="w-full cursor-pointer p-3 rounded-lg border-2 transition-colors duration-200 flex items-center"
              :class="isAllPartnersSelected
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'"
            >
              <input
                type="checkbox"
                :checked="isAllPartnersSelected"
                @change="toggleAllPartners"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <svg class="w-5 h-5 mx-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              <div>
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ authStore.isSuperUser ? 'All Partners' : 'View All Partners' }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ authStore.isSuperUser ? 'View data from all partners' : 'View data from all your assigned partners' }}
                </div>
              </div>
            </label>
          </div>

          <!-- Loading State -->
          <div v-if="searching" class="text-center py-8">
            <svg class="animate-spin h-8 w-8 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            <p class="text-gray-500 dark:text-gray-400">Searching partners...</p>
          </div>

          <!-- Partner List -->
          <div v-else-if="displayPartners.length > 0" class="space-y-2">
            <label
              v-for="partner in displayPartners"
              :key="partner.id"
              class="w-full cursor-pointer p-3 rounded-lg border-2 transition-colors duration-200 flex items-center"
              :class="selectedPartnerIds.includes(partner.id.toString())
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'"
            >
              <input
                type="checkbox"
                :value="partner.id.toString()"
                :checked="selectedPartnerIds.includes(partner.id.toString())"
                @change="togglePartner(partner.id.toString())"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <svg class="w-5 h-5 mx-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
              <div class="flex-1">
                <div class="flex items-center">
                  <div class="font-medium text-gray-900 dark:text-white">{{ partner.name }}</div>
                  <span v-if="selectedPartnerIds.includes(partner.id.toString())"
                        class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200">
                    Selected
                  </span>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  ID: {{ partner.id }}
                  <span v-if="partner.country"> • {{ partner.country }}</span>
                </div>
              </div>
            </label>
          </div>

          <!-- No Partners -->
          <div v-else class="text-center py-8">
            <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Partners</h3>
            <p class="text-gray-500 dark:text-gray-400">No partners are assigned to your account.</p>
          </div>
          </div>
        </div>

        <!-- Selected Tab Content -->
        <div v-else-if="activeTab === 'selected'" class="flex-1 overflow-hidden flex flex-col">
          <div class="flex-1 p-4 overflow-y-auto">
          <div v-if="selectedPartners.length > 0" class="space-y-2">
            <div
              v-for="partner in selectedPartners"
              :key="partner.id"
              class="flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-blue-50 dark:bg-blue-900/20"
            >
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">{{ partner.name }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    ID: {{ partner.id }}
                    <span v-if="partner.country"> • {{ partner.country }}</span>
                  </div>
                </div>
              </div>
              <button
                @click="removeFromSelection(partner.id.toString())"
                class="text-red-500 hover:text-red-700 p-1"
                title="Remove from selection"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            </div>
          </div>

          <div v-else class="text-center py-8">
            <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Partners Selected</h3>
            <p class="text-gray-500 dark:text-gray-400">Switch to the Search tab to select partners.</p>
          </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-between items-center p-4 border-t border-gray-200 dark:border-gray-700">
          <!-- Selection Summary -->
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <span v-if="selectedPartnerIds.length === 0 && !isAllPartnersSelected">No partners selected</span>
            <span v-else-if="isAllPartnersSelected">All partners selected ({{ partnerList.length }})</span>
            <span v-else-if="selectedPartnerIds.length === 1">{{ selectedPartners[0]?.name || 'Unknown Partner' }}</span>
            <span v-else>{{ selectedPartnerIds.length }} Partners</span>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-3">
            <button
              @click="closeModal"
              class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              @click="applySelection"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { partnerApi } from '@/services/partnerApi'

// Props
// interface Props {
//   isOpen: boolean
// }

// defineProps<Props>()

interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  refresh: []
}>()

// Stores
const authStore = useAuthStore()

// Reactive data
const searchQuery = ref('')
const searchResults = ref<any[]>([])
const searching = ref(false)
const selectedPartnerIds = ref<string[]>([])
const selectedPartners = ref<any[]>([]) // Store full partner objects
const activeTab = ref<'search' | 'selected'>('search')
const loadedPartners = ref<any[]>([]) // Store partners loaded from API
const loading = ref(false)
let searchTimeout: ReturnType<typeof setTimeout> | null = null

// Initialize selected partners from current auth store selection
const initializeSelection = () => {
  const currentSelection = authStore.selectedPartnerId
  selectedPartnerIds.value = []
  selectedPartners.value = []

  if (currentSelection && currentSelection !== 'all' && currentSelection.trim() !== '') {
    // If it's a comma-separated list, split it
    const partnerIds = currentSelection.includes(',')
      ? currentSelection.split(',').filter(id => id.trim())
      : [currentSelection]

    selectedPartnerIds.value = partnerIds

    // Wait for partners to be loaded before finding objects
    setTimeout(() => {
      selectedPartners.value = partnerList.value.filter(partner =>
        partnerIds.includes(partner.id.toString())
      )
    }, 100)
  }
}

// Computed
const partnerList = computed(() => {
  // Use loaded partners if available, otherwise fall back to auth store
  return loadedPartners.value.length > 0 ? loadedPartners.value : (authStore.partnerList || [])
})

const displayPartners = computed(() => {
  let partners = []
  if (authStore.isSuperUser && searchQuery.value.trim()) {
    partners = searchResults.value
  } else {
    partners = partnerList.value
  }

  // For admin users with search, show all search results (don't filter selected ones)
  // For regular partner list, filter out selected partners to avoid duplication
  if (authStore.isSuperUser && searchQuery.value.trim()) {
    return partners // Show all search results
  } else {
    return partners.filter(partner => !selectedPartnerIds.value.includes(partner.id.toString()))
  }
})

const isAllPartnersSelected = computed(() => {
  if (partnerList.value.length === 0) return false
  return partnerList.value.every(partner => selectedPartnerIds.value.includes(partner.id.toString()))
})



// Methods
const closeModal = () => {
  emit('close')
}

const togglePartner = (partnerId: string) => {
  const index = selectedPartnerIds.value.indexOf(partnerId)
  if (index > -1) {
    // Remove from selection
    selectedPartnerIds.value.splice(index, 1)
    selectedPartners.value = selectedPartners.value.filter(p => p.id.toString() !== partnerId)
  } else {
    // Add to selection
    selectedPartnerIds.value.push(partnerId)
    const partner = partnerList.value.find(p => p.id.toString() === partnerId)
    if (partner) {
      selectedPartners.value.push(partner)
    }
  }
}

const toggleAllPartners = () => {
  if (isAllPartnersSelected.value) {
    // Unselect all
    selectedPartnerIds.value = []
    selectedPartners.value = []
  } else {
    // Select all
    selectedPartnerIds.value = partnerList.value.map(partner => partner.id.toString())
    selectedPartners.value = [...partnerList.value]
  }
}

const removeFromSelection = (partnerId: string) => {
  const index = selectedPartnerIds.value.indexOf(partnerId)
  if (index > -1) {
    selectedPartnerIds.value.splice(index, 1)
    selectedPartners.value = selectedPartners.value.filter(p => p.id.toString() !== partnerId)
  }
}

const applySelection = async () => {
  let partnerIdsToApply = ''

  if (isAllPartnersSelected.value) {
    if (authStore.isSuperUser) {
      // Admin users: send empty string to get all data
      partnerIdsToApply = ''
    } else {
      // Partner users: send comma-separated list of their partner IDs
      partnerIdsToApply = partnerList.value.map(p => p.id).join(',')
    }
  } else if (selectedPartnerIds.value.length > 0) {
    partnerIdsToApply = selectedPartnerIds.value.join(',')
  } else {
    // No selection
    if (authStore.isSuperUser) {
      // Admin users: empty string for no selection
      partnerIdsToApply = ''
    } else {
      // Partner users: send their partner IDs when nothing is selected
      partnerIdsToApply = partnerList.value.map(p => p.id).join(',')
    }
  }

  await authStore.selectPartner(partnerIdsToApply)

  closeModal()

  // Trigger partner selection change event for all listening components
  window.dispatchEvent(new CustomEvent('partner-selection-changed', {
    detail: {
      selectedPartnerId: partnerIdsToApply,
      timestamp: Date.now()
    }
  }))

  // Emit refresh event to parent component
  emit('refresh')
}

const searchPartners = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    return
  }

  searching.value = true
  try {
    const response = await partnerApi.getPartners({
      search: searchQuery.value.trim(),
      limit: 50,
      partner_ids: ' ' // Empty string for admin to search all partners
    })

    if (response.status === 200) {
      searchResults.value = response.message?.data || []
    } else {
      searchResults.value = []
    }
  } catch (error) {
    console.error('Partner search error:', error)
    searchResults.value = []
  } finally {
    searching.value = false
  }
}

const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    searchPartners()
  }, 300)
}

// Load partners from API
const loadPartners = async () => {
  if (authStore.isSuperUser) {
    loading.value = true
    try {
      const response = await partnerApi.getPartners({
        page: 1,
        limit: 100 // Load more partners for admin users
      })

      if (response.status === 200 && response.message?.data?.result) {
        loadedPartners.value = response.message.data.result
      }
    } catch (error) {
      console.error('Failed to load partners:', error)
    } finally {
      loading.value = false
    }
  }
}

// Initialize selection when modal opens
const initializeOnOpen = () => {
  initializeSelection()
  loadPartners() // Load partners when modal opens
}

// Watch for modal open state to initialize selection
watch(() => props.isOpen, (isOpenValue: boolean) => { // Add type annotation
  if (isOpenValue) {
    initializeOnOpen()
  }
}, { immediate: true })

// Load partners on component mount
onMounted(() => {
  loadPartners()
})

</script>
