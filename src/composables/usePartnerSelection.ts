import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * Composable for handling partner selection changes across components
 * This allows any component to listen for partner selection changes and refresh its data
 */
export function usePartnerSelection() {
  const authStore = useAuthStore()
  const isMounted = ref(false)
  const isRefreshing = ref(false)

  /**
   * Register a callback to be called when partner selection changes
   * @param callback - Function to call when partner selection changes
   * @param immediate - Whether to call the callback immediately on mount
   */
  const onPartnerSelectionChange = (callback: () => void | Promise<void>, immediate = false) => {
    const handlePartnerSelectionChange = async () => {
      // Only refresh if component is still mounted and not already refreshing
      if (isMounted.value && !isRefreshing.value) {
        isRefreshing.value = true
        try {
          await callback()
        } catch (error) {
          console.error('Error during partner selection refresh:', error)
        } finally {
          isRefreshing.value = false
        }
      }
    }

    onMounted(() => {
      isMounted.value = true
      // Listen for partner selection changes
      window.addEventListener('partner-selection-changed', handlePartnerSelectionChange)
      
      // Call immediately if requested
      if (immediate) {
        handlePartnerSelectionChange()
      }
    })

    onUnmounted(() => {
      isMounted.value = false
      window.removeEventListener('partner-selection-changed', handlePartnerSelectionChange)
    })

    return {
      isRefreshing,
      isMounted
    }
  }

  /**
   * Trigger a partner selection change event
   * This should be called when the partner selection is updated
   */
  const triggerPartnerSelectionChange = () => {
    window.dispatchEvent(new CustomEvent('partner-selection-changed', {
      detail: {
        selectedPartnerId: authStore.selectedPartnerId,
        timestamp: Date.now()
      }
    }))
  }

  /**
   * Get current partner selection parameters for API calls
   */
  const getPartnerParams = () => {
    return {
      partner_ids: authStore.selectedPartnerId || ''
    }
  }

  /**
   * Check if a specific partner is selected
   */
  const isPartnerSelected = (partnerId: string) => {
    const selectedId = authStore.selectedPartnerId
    if (!selectedId) return false
    
    if (selectedId.includes(',')) {
      return selectedId.split(',').includes(partnerId)
    }
    
    return selectedId === partnerId
  }

  /**
   * Get list of selected partner IDs
   */
  const getSelectedPartnerIds = (): string[] => {
    const selectedId = authStore.selectedPartnerId
    if (!selectedId) return []
    
    if (selectedId.includes(',')) {
      return selectedId.split(',').filter(id => id.trim())
    }
    
    return [selectedId]
  }

  return {
    onPartnerSelectionChange,
    triggerPartnerSelectionChange,
    getPartnerParams,
    isPartnerSelected,
    getSelectedPartnerIds,
    isRefreshing,
    isMounted
  }
}
