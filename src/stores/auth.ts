import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
// Router will be imported dynamically to avoid circular dependency
import { authApi } from '@/services/authApi'
import { setToken, getToken, setSelectedPartnerId, getSelectedPartnerId, clearAuth, clearSelectedPartner } from '@/utils/auth'
import { dashboardCache } from '@/services/dashboardCache'

// Types
interface User {
  id?: number
  username?: string
  display_name?: string
  email?: string
  role_id: string
  role_name?: string // Role display name
  permissions?: Permission[]
  token?: string
  mc?: number
  partners?: any[] // Partner accounts
  // New fields from API response
  un?: string // User name (display name)
  cn?: string // Company name
  cid?: string // Company ID
  uid?: string // User ID
  expires?: string | number // Token expiration
  // Enhanced user fields from new API response
  user_type?: string // Admin or Partner
  rid?: string // Role ID
  rname?: string // Role name for display
  type?: string // Token expiration type (minute, hour, etc.)
  partner_count?: string | number
  // OTP related fields
  requires_otp?: boolean
  otp_expires_in?: number
  otp_sent?: boolean
}

interface Permission {
  id: string | number // Can be string from API
  name: string
  description?: string
}



interface LoginCredentials {
  username: string
  password: string
  dial_code: string
}

interface LoginWithCodeCredentials extends LoginCredentials {
  verification_code: string
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const token = ref<string | null>(getToken())
  const permissions = ref<number[]>([])
  const role = ref<number | null>(null)
  const isSuperRole = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Partner management
  const selectedPartnerId = ref<string | null>(getSelectedPartnerId())
  const partnerList = ref<any[]>(JSON.parse(localStorage.getItem('partnerList') || '[]'))
  const hasMultiplePartners = computed(() => partnerList.value.length > 1)
  const selectedPartner = computed(() => {
    if (selectedPartnerId.value === 'all' || !selectedPartnerId.value) {
      return { id: 'all', name: 'All Partners' }
    }
    return partnerList.value.find(p => p.id.toString() === selectedPartnerId.value) || null
  })

  // Super roles configuration (Admin roles that can see all partners)
  const superRoles = [1, 2]

  // OTP context for preserving login state
  const otpContext = ref<{
    userData?: any
    credentials?: any
    timestamp?: number
  } | null>(null)

  // Computed
  const isAuthenticated = computed(() => !!token.value)
  const hasPermission = computed(() => (permissionId: number) =>
    permissions.value.includes(permissionId)
  )
  const isSuperUser = computed(() =>
    role.value ? superRoles.includes(role.value) : false
  )
  const isPartner = computed(() =>
    user.value?.user_type === 'Partner'
  )
  const isAdmin = computed(() =>
    user.value?.user_type === 'Admin'
  )
  const isPartnerAdmin = computed(() =>
    role.value === 5 // Partner Admin role ID
  )

  // Privacy mode state
  const isPrivateMode = ref(false)

  // Enhanced permission checking methods
  const hasPermissionByName = (permissionName: string): boolean => {
    if (isSuperUser.value) return true
    // This would need to be enhanced with actual permission name mapping
    // For now, return true for super users and false for others
    return false
  }

  const hasAnyPermission = (permissionIds: number[]): boolean => {
    if (isSuperUser.value) return true
    return permissionIds.some(permissionId => permissions.value.includes(permissionId))
  }

  const hasAllPermissions = (permissionIds: number[]): boolean => {
    if (isSuperUser.value) return true
    return permissionIds.every(permissionId => permissions.value.includes(permissionId))
  }

  const hasRole = (roleId: number): boolean => {
    return role.value === roleId
  }

  const hasAnyRole = (roleIds: number[]): boolean => {
    return role.value ? roleIds.includes(role.value) : false
  }

  // Module-based access checking
  const hasModuleAccess = (modulePermissions: number[]): boolean => {
    if (isSuperUser.value) return true
    return hasAnyPermission(modulePermissions)
  }

  // Actions
  /**
   * Authenticate user with credentials
   */
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      const payload = {
        ...credentials,
        password: btoa(credentials.password)
      }

      const response = await authApi.login(payload)

      if (response.status === 200) {
        const userData = response.message.data

        // Check if OTP verification is required
        if (userData.requires_otp) {
          return {
            success: false,
            requiresCode: true,
            message: response.message.message || 'Please verify with the OTP sent to your mobile number.',
            data: userData
          }
        }

        // Full login success - set user data with enhanced fields
        user.value = {
          ...userData,
          username: userData.username || userData.un || '',
          display_name: userData.display_name || userData.un || userData.username || '',
          id: parseInt(userData.cid || userData.uid || '0')
        } as User
        // Try different possible token field names
        const userToken = userData.token || (userData as any).access_token || (userData as any).auth_token || ''

        token.value = userToken
        role.value = parseInt(userData.role_id || (userData as any).rid || '0')

        const permissionIds = extractPermissionIds(userData.permissions)
        permissions.value = permissionIds

        isSuperRole.value = superRoles.includes(role.value)

        localStorage.setItem('token', userToken)
        localStorage.setItem('user', JSON.stringify(user.value))
        localStorage.setItem('permissions', JSON.stringify(permissionIds))
        localStorage.setItem('role', role.value.toString())

        return { success: true, requiresCode: false, data: userData }
      } else if (response.status === 205 || response.status === 201 || response.status === 410) {
        const errorMsg = response.message?.message || 'Verification code required'
        return { success: false, requiresCode: true, message: errorMsg }
      } else {
        const errorMsg = response.message?.message || 'Login failed'
        error.value = errorMsg
        return { success: false, requiresCode: false, message: errorMsg }
      }
    } catch (err: any) {
      error.value = err.message || 'Login failed'
      return { success: false, requiresCode: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Resend OTP code (password is already base64 encoded)
   */
  const resendOTP = async (credentials: LoginCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      // Don't re-encode password as it's already base64 encoded
      const response = await authApi.login(credentials)

      if (response.status === 200) {
        const userData = response.message.data

        // Check if OTP verification is required
        if (userData.requires_otp) {
          return {
            success: true,
            requiresCode: true,
            message: response.message.message || 'Verification code resent successfully.',
            data: userData
          }
        }

        // If no OTP required, something went wrong
        return {
          success: false,
          message: 'Unexpected response: OTP not required'
        }
      } else {
        const errorMsg = typeof response.message === 'string' ? response.message : response.message?.message || 'Failed to resend OTP'
        error.value = errorMsg
        return { success: false, message: errorMsg }
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to resend OTP'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Authenticate user with verification code
   */
  const loginWithCode = async (credentials: LoginWithCodeCredentials) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authApi.loginWithCode(credentials)

      if (response.status === 200) {
        // Handle new nested response format - token is in response.message.data.data
        const responseData = response.message.data || response.message
        const userData = (responseData as any).data || responseData



        // Set user data with proper mapping and enhanced fields
        user.value = {
          ...userData,
          username: userData.username || userData.un || '',
          display_name: userData.display_name || userData.un || userData.username || '',
          id: parseInt(userData.cid || userData.uid || '0'),
          // Enhanced fields from new API response
          user_type: userData.user_type || '',
          rid: userData.rid || userData.role_id || '',
          rname: userData.rname || userData.role_name || '',
          expires: userData.expires || '',
          type: userData.type || '',
          partner_count: userData.partner_count || '0'
        }
        token.value = userData.token || ''
        role.value = parseInt(userData.role_id || userData.rid || '0')


        // Extract and set permissions
        const permissionIds = extractPermissionIds(userData.permissions)
        permissions.value = permissionIds

        // Check if super role
        isSuperRole.value = superRoles.includes(role.value)

        // Multi-client scenario handling removed as per requirements

        // Handle partner accounts based on user type
        const userType = userData.user_type || ''
        const isPartnerUser = userType === 'Partner'
        const isAdminUser = superRoles.includes(role.value) // roles 1,2


        
        // Store in localStorage and update reactive refs
        // Try different possible token field names
        const userToken = userData.token || (userData as any).access_token || (userData as any).auth_token || ''



        // Use centralized token management
        setToken(token.value as string)
        // setToken(userToken)
        token.value = userToken
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('permissions', JSON.stringify(permissionIds))
        localStorage.setItem('role', role.value.toString())

        if (userData.partners && Array.isArray(userData.partners)) {
          partnerList.value = userData.partners
          localStorage.setItem('partnerList', JSON.stringify(userData.partners))

          if (isAdminUser) {
            // Admin users - go to dashboard without partner selection, can see all data
            setSelectedPartnerId('all')
            selectedPartnerId.value = 'all'

          } else if (isPartnerUser) {
            // Partner users - must select from their assigned partners
            if (userData.partners.length === 0) {
              // No partners - user has no partner access
              clearSelectedPartner()
              selectedPartnerId.value = null

            } else if (userData.partners.length === 1) {
              // Single partner - auto-select it
              const partnerId = userData.partners[0].id.toString()
              setSelectedPartnerId(partnerId)
              selectedPartnerId.value = partnerId

            } else {
              // Multiple partners - require selection

              return { success: true, requiresPartnerSelection: true, partners: userData.partners }
            }
          } else {
            // Other user types - handle based on partner availability
            if (userData.partners.length === 1) {
              const partnerId = userData.partners[0].id.toString()
              setSelectedPartnerId(partnerId)
              selectedPartnerId.value = partnerId
            } else {
              setSelectedPartnerId('all')
              selectedPartnerId.value = 'all'
            }
          }
        } else {
          // No partners data - clear partner selection
          partnerList.value = []
          if (isAdminUser) {
            setSelectedPartnerId('all')
            selectedPartnerId.value = 'all'
          } else {
            clearSelectedPartner()
            selectedPartnerId.value = null
          }
          localStorage.removeItem('partnerList')
        }



        return { success: true, requiresClientSelection: false, data: userData }
      } else {
        const errorMsg = typeof response.message === 'string' ? response.message : response.message?.message || 'Login with code failed'
        error.value = errorMsg
        return { success: false, message: errorMsg }
      }
    } catch (err: any) {
      error.value = err.message || 'Login with code failed'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }



  const forgotPassword = async (username: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await authApi.forgotPassword({ username, dial_code: '254' })

      if (response.status === 200) {
        return { success: true, message: response.message }
      } else {
        error.value = response.message
        return { success: false, message: response.message }
      }
    } catch (err: any) {
      error.value = err.message || 'Password reset failed'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async (reason?: string) => {
    // No API call needed - just clear local data
    console.log('Logging out user:', user.value?.username || 'Unknown user')

    // Clear state
    user.value = null
    token.value = null
    permissions.value = []
    role.value = null
    isSuperRole.value = false
    error.value = null
    selectedPartnerId.value = null
    partnerList.value = []

    // Use centralized auth clearing
    clearAuth()

    // Clear OTP context
    clearOtpContext()

    // Set error message if logout was due to session expiration
    if (reason && reason.toLowerCase().includes('session')) {
      error.value = reason
    }

    // Redirect to login using dynamic import
    const { router } = await import('@/router')
    router.push({ name: 'login' })
  }

  const initializeAuth = () => {
    // Restore auth state from localStorage
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    const storedPermissions = localStorage.getItem('permissions')
    const storedRole = localStorage.getItem('role')

    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)

      if (storedPermissions) {
        permissions.value = JSON.parse(storedPermissions)
      }

      if (storedRole) {
        role.value = parseInt(storedRole)
        isSuperRole.value = superRoles.includes(role.value)
      }
    }
  }

  // Helper functions
  const extractPermissionIds = (permissionsData: Permission[] | undefined): number[] => {
    if (!permissionsData || !Array.isArray(permissionsData)) {
      return []
    }
    return permissionsData.map(permission => {
      // Convert string IDs to numbers if needed
      return typeof permission.id === 'string' ? parseInt(permission.id) : permission.id
    })
  }

  const clearError = () => {
    error.value = null
  }

  /**
   * Set OTP context for preserving login state
   */
  const setOtpContext = (userData: any, credentials: any) => {
    otpContext.value = {
      userData,
      credentials,
      timestamp: Date.now()
    }
  }

  /**
   * Get OTP context if still valid (within 10 minutes)
   */
  const getOtpContext = () => {
    if (!otpContext.value) return null

    const tenMinutes = 10 * 60 * 1000
    const isExpired = Date.now() - (otpContext.value.timestamp || 0) > tenMinutes

    if (isExpired) {
      otpContext.value = null
      return null
    }

    return otpContext.value
  }

  /**
   * Clear OTP context
   */
  const clearOtpContext = () => {
    otpContext.value = null
  }

  /**
   * Select a partner account
   */
  const selectPartner = async (partnerId: string) => {
    // Clear dashboard cache when partner selection changes
    const previousPartnerId = selectedPartnerId.value
    if (previousPartnerId !== partnerId) {
      // Clear cache for the previous partner selection
      if (previousPartnerId) {
        dashboardCache.clearForPartner(previousPartnerId)
      }
      // Clear cache for the new partner selection
      dashboardCache.clearForPartner(partnerId)
    }

    // Use centralized partner management
    setSelectedPartnerId(partnerId)
    selectedPartnerId.value = partnerId

    // You might want to call an API to set the partner context
    // await authApi.setPartnerContext(partnerId)

    return { success: true }
  }

  /**
   * Get current selected partner
   */
  const getCurrentPartner = () => {
    if (!selectedPartnerId.value || !partnerList.value.length) return null
    return partnerList.value.find(partner => partner.id === selectedPartnerId.value)
  }

  // Privacy mode methods
  const togglePrivateMode = () => {
    isPrivateMode.value = !isPrivateMode.value
    localStorage.setItem('privacy_mode', isPrivateMode.value.toString())
  }

  const setPrivateMode = (enabled: boolean) => {
    isPrivateMode.value = enabled
    localStorage.setItem('privacy_mode', enabled.toString())
  }

  const initializePrivacyMode = () => {
    const savedMode = localStorage.getItem('privacy_mode')
    if (savedMode !== null) {
      isPrivateMode.value = savedMode === 'true'
    }
  }

  // Initialize auth state when store is created
  initializeAuth()
  initializePrivacyMode()

  return {
    // State
    user,
    token,
    permissions,
    role,
    isSuperRole,
    isLoading,
    error,
    selectedPartnerId,
    partnerList,
    hasMultiplePartners,
    superRoles,

    // Computed
    isAuthenticated,
    hasPermission,
    isSuperUser,
    isPartner,
    isAdmin,
    isPartnerAdmin,

    // Privacy mode
    isPrivateMode,

    // Enhanced permission methods
    hasPermissionByName,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    hasModuleAccess,

    // Actions
    login,
    loginWithCode,
    resendOTP,
    forgotPassword,
    logout,
    initializeAuth,
    clearError,

    // OTP context management
    setOtpContext,
    getOtpContext,
    clearOtpContext,

    // Partner management
    selectedPartner,
    selectPartner,
    getCurrentPartner,

    // Privacy mode
    togglePrivateMode,
    setPrivateMode
  }
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    pick: ['token', 'user', 'permissions', 'role', 'selectedPartnerId']
  }
})
