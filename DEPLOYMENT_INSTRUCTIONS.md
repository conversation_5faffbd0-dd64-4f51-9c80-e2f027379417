# Deployment Instructions - Fix for Asset Loading Issues

## Problem
Getting 404 errors for assets like:
- `GET https://b2b.bo.mb-gaming.life/app/assets/index-Cfj_AWhI.css net::ERR_ABORTED 404`
- `GET https://b2b.bo.mb-gaming.life/app/assets/index-DxX_LmJp.js net::ERR_ABORTED 404`

## Root Cause
The assets are not being served correctly because the server directory structure doesn't match the expected paths.

## Solution Options

### Option 1: Deploy to /app/ subdirectory (Recommended)

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Create the correct directory structure on your server:**
   ```bash
   # On your server
   sudo mkdir -p /var/www/html/app
   ```

3. **Upload files to the correct location:**
   - Upload ALL contents of the `dist` folder to `/var/www/html/app/` (not `/var/www/html/`)
   - Your server structure should look like:
     ```
     /var/www/html/app/
     ├── index.html
     ├── assets/
     │   ├── index-Cfj_AWhI.css
     │   ├── index-DxX_LmJp.js
     │   └── vendor-DGybOs-S.js
     └── favicon.ico
     ```

4. **Update Nginx configuration:**
   Use the updated `nginx.conf` file and modify the root path:
   ```nginx
   server {
       listen 80;
       server_name b2b.bo.mb-gaming.life;
       root /var/www/html;  # This should point to the parent of 'app' folder
       index index.html;
   ```

5. **Restart Nginx:**
   ```bash
   sudo systemctl restart nginx
   ```

### Option 2: Deploy to root with /app/ base (Alternative)

If you prefer to keep files in the root directory:

1. **Update Vite config to use root deployment:**
   ```typescript
   // In vite.config.ts, change:
   base: '/', // Instead of '/app/'
   ```

2. **Update .env.production:**
   ```
   BASE_URL=/
   ```

3. **Update Nginx config:**
   ```nginx
   location / {
       try_files $uri $uri/ /index.html;
   }
   ```

4. **Upload dist contents to `/var/www/html/` directly**

## Testing Deployment

1. **Check file accessibility directly:**
   - Visit: `https://b2b.bo.mb-gaming.life/app/assets/index-Cfj_AWhI.css`
   - Should return the CSS file, not a 404

2. **Test the application:**
   - Visit: `https://b2b.bo.mb-gaming.life/app/`
   - Navigate to different pages
   - Refresh pages to test SPA routing

## Troubleshooting

### If assets still return 404:

1. **Check file permissions:**
   ```bash
   sudo chown -R www-data:www-data /var/www/html/app
   sudo chmod -R 755 /var/www/html/app
   ```

2. **Check Nginx error logs:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

3. **Verify file structure:**
   ```bash
   ls -la /var/www/html/app/
   ls -la /var/www/html/app/assets/
   ```

### If you get "file not found" errors:

1. **Check Nginx configuration syntax:**
   ```bash
   sudo nginx -t
   ```

2. **Ensure the correct site is enabled:**
   ```bash
   sudo ln -s /etc/nginx/sites-available/your-site /etc/nginx/sites-enabled/
   ```

## Quick Fix Script

Create this script on your server to automate the deployment:

```bash
#!/bin/bash
# deploy-to-server.sh

# Remove old files
sudo rm -rf /var/www/html/app/*

# Create app directory if it doesn't exist
sudo mkdir -p /var/www/html/app

# Copy new files (assuming you uploaded dist.tar.gz)
sudo tar -xzf dist.tar.gz -C /var/www/html/app/

# Set correct permissions
sudo chown -R www-data:www-data /var/www/html/app
sudo chmod -R 755 /var/www/html/app

# Restart nginx
sudo systemctl restart nginx

echo "Deployment complete!"
```

## Expected URLs After Deployment

- **App**: `https://b2b.bo.mb-gaming.life/app/`
- **Partners**: `https://b2b.bo.mb-gaming.life/app/partners`
- **Assets**: `https://b2b.bo.mb-gaming.life/app/assets/[filename]`
